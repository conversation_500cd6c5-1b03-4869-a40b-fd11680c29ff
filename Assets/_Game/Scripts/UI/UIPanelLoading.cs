using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Extensions;
using OnePuz.Services;
using PrimeTween;
using Spine.Unity;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace OnePuz.UI
{
    public class UIPanelLoading : MonoBehaviour
    {
        [SerializeField]
        private UISlider _slider;

        [SerializeField]
        private SkeletonGraphic _titleAnimation;

        [SerializeField, SpineAnimation]
        private string _showTitleAnimation;

        [SerializeField, SpineAnimation]
        private string _idleTitleAnimation;

        private void Awake()
        {
            HandleLoading().Forget();
        }

        private async UniTaskVoid HandleLoading()
        {
           OLogger.Log("Loading game....");

            var cancellationToken = gameObject.GetCancellationTokenOnDestroy();

            _titleAnimation.AnimationState.SetAnimation(0, _showTitleAnimation, false);
            _titleAnimation.AnimationState.AddAnimation(0, _idleTitleAnimation, true, 0f);
            
            _slider.Init();
            _slider.ProcessAsync(100, 8f, cancellationToken).Forget();

            await UniTask.DelayFrame(2, cancellationToken: cancellationToken);
            await Bootstrap.LoadAdditionalAsync(enableDelay: true);
            Core.UI.LoadAdditional();

            var initialStartTime = Time.timeSinceLevelLoad;
            await UniTask.WaitUntil(() => (Core.Get<RemoteService>().HasFetched &&
                                           Core.Ads.HasInitialized &&
                                           Core.IAP.HasInitialized) ||
                                          Time.timeSinceLevelLoad - initialStartTime >= 8f, cancellationToken: cancellationToken);

            OLogger.Log("Finished waiting for remote service, ads and iap initialization");
            var enableAppOpenAds = DataShortcut.Ads.enableAOAForOpenGame && !DataShortcut.Ads.isAdsRemoved && DataShortcut.Ads.enableAds;

            if (enableAppOpenAds || (DataShortcut.User.openedGameTime > 1 || DataShortcut.Ads.enableAppOpenAdsOnFirstOpen))
            {
                await UniTask.WaitUntil(() => Core.Ads.IsAppOpenAdLoaded() || Time.timeSinceLevelLoad - initialStartTime >= 8f, cancellationToken: cancellationToken);
            }

            await _slider.ProcessAsync(100, 1f, cancellationToken);

            OLogger.Log("### Finished loading game....");

            if (enableAppOpenAds && (DataShortcut.User.openedGameTime > 1 || DataShortcut.Ads.enableAppOpenAdsOnFirstOpen))
                Core.Ads.ShowAppOpenAd();

            await UniTask.Delay(TimeSpan.FromSeconds(0.5f), cancellationToken: cancellationToken);

            DataShortcut.User.openedGameTime++;
            OLogger.CrashlyticsSetKey("opened_time", DataShortcut.User.openedGameTime.ToString());
            OLogger.CrashlyticsSetUserId(SystemInfo.deviceUniqueIdentifier);

            if (DataShortcut.Level.Current >= 1)
                Core.Home();
            else
                Core.Game();
        }
    }
}