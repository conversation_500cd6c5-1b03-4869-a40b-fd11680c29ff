using UnityEngine;
using Kamgam.SandGame;

namespace Test
{
    /// <summary>
    /// Test script to verify that PixelWorld viewport and PixelCanvas scale remain fixed
    /// when camera orthographicSize changes after level loading.
    /// </summary>
    public class PixelWorldFixedScaleTest : MonoBehaviour
    {
        [Header("References")]
        public PixelWorld pixelWorld;
        public Camera testCamera;
        
        [Header("Test Settings")]
        public float initialOrthographicSize = 5f;
        public float testOrthographicSize = 10f;
        public KeyCode testKey = KeyCode.T;
        public KeyCode resetKey = KeyCode.R;
        public KeyCode unfixKey = KeyCode.U;
        
        private Vector3 _initialCanvasScale;
        private int _initialViewportWidth;
        private int _initialViewportHeight;
        private bool _testStarted = false;

        void Start()
        {
            if (testCamera == null)
                testCamera = Camera.main;
                
            if (pixelWorld == null)
                pixelWorld = FindObjectOfType<PixelWorld>();
                
            if (testCamera != null)
                testCamera.orthographicSize = initialOrthographicSize;
        }

        void Update()
        {
            if (Input.GetKeyDown(testKey))
            {
                TestFixedScale();
            }
            
            if (Input.GetKeyDown(resetKey))
            {
                ResetTest();
            }
            
            if (Input.GetKeyDown(unfixKey))
            {
                UnfixTest();
            }
        }

        void TestFixedScale()
        {
            if (pixelWorld == null || testCamera == null)
            {
                Debug.LogError("PixelWorld or Camera is null!");
                return;
            }

            if (!_testStarted)
            {
                // Store initial values
                _initialCanvasScale = pixelWorld.Canvas.RenderTarget.transform.localScale;
                _initialViewportWidth = pixelWorld.ViewportWidth;
                _initialViewportHeight = pixelWorld.ViewportHeight;
                _testStarted = true;
                
                Debug.Log($"Initial Canvas Scale: {_initialCanvasScale}");
                Debug.Log($"Initial Viewport: {_initialViewportWidth}x{_initialViewportHeight}");
                Debug.Log($"Canvas Scale Fixed: {pixelWorld.Canvas.IsScaleFixed}");
                Debug.Log($"Viewport Fixed: {pixelWorld.IsViewportFixed}");
            }

            // Change camera orthographic size
            testCamera.orthographicSize = testOrthographicSize;
            
            // Check if values remained fixed
            Vector3 currentCanvasScale = pixelWorld.Canvas.RenderTarget.transform.localScale;
            int currentViewportWidth = pixelWorld.ViewportWidth;
            int currentViewportHeight = pixelWorld.ViewportHeight;
            
            Debug.Log($"After changing orthographicSize to {testOrthographicSize}:");
            Debug.Log($"Canvas Scale: {currentCanvasScale} (Should be: {_initialCanvasScale})");
            Debug.Log($"Viewport: {currentViewportWidth}x{currentViewportHeight} (Should be: {_initialViewportWidth}x{_initialViewportHeight})");
            
            bool scaleFixed = Vector3.Approximately(currentCanvasScale, _initialCanvasScale);
            bool viewportFixed = (currentViewportWidth == _initialViewportWidth && currentViewportHeight == _initialViewportHeight);
            
            Debug.Log($"Scale Fixed: {scaleFixed} | Viewport Fixed: {viewportFixed}");
            
            if (scaleFixed && viewportFixed)
            {
                Debug.Log("<color=green>TEST PASSED: Scale and viewport remained fixed!</color>");
            }
            else
            {
                Debug.Log("<color=red>TEST FAILED: Scale or viewport changed!</color>");
            }
        }

        void ResetTest()
        {
            if (testCamera != null)
            {
                testCamera.orthographicSize = initialOrthographicSize;
                Debug.Log($"Reset camera orthographicSize to {initialOrthographicSize}");
            }
            _testStarted = false;
        }

        void UnfixTest()
        {
            if (pixelWorld != null)
            {
                pixelWorld.UnfixViewportAndScale();
                Debug.Log("Unfixed viewport and scale - they should now respond to camera changes");
            }
        }

        void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 400, 200));
            GUILayout.Label("PixelWorld Fixed Scale Test");
            GUILayout.Label($"Press {testKey} to test fixed scale");
            GUILayout.Label($"Press {resetKey} to reset camera");
            GUILayout.Label($"Press {unfixKey} to unfix scale/viewport");
            
            if (pixelWorld != null && pixelWorld.Canvas != null)
            {
                GUILayout.Label($"Canvas Scale Fixed: {pixelWorld.Canvas.IsScaleFixed}");
                GUILayout.Label($"Viewport Fixed: {pixelWorld.IsViewportFixed}");
                GUILayout.Label($"Current Orthographic Size: {(testCamera != null ? testCamera.orthographicSize.ToString("F2") : "N/A")}");
            }
            GUILayout.EndArea();
        }
    }
}
